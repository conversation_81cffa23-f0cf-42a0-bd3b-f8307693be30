// Owner: xuyz
// Co-Owner: zhuxf

#pragma once

#include "GcmpModelInterface.h"

namespace gcmp
{   
    /// \brief 图形样式数据中各属性的有效性设置
    class GCMP_MODEL_INTERFACE_EXPORT IGraphicsStyleDataValiditySettings
    {
    public:
        /// \brief 析构函数
        virtual ~IGraphicsStyleDataValiditySettings() {}

#pragma region static methods
        /// \brief 创建接口实例
        /// \return 新创建的接口实例
        static OwnerPtr<IGraphicsStyleDataValiditySettings> Create();
#pragma endregion static methods

        /// \brief 使所有属性无效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& ValidateNone() = 0;

        /// \brief 是否所有属性都无效
        /// \return true-所有字段都无效，false-存在有效字段
        virtual bool IsNoneValid() const = 0;

        /// \brief 使所有属性有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& ValidateAll() = 0;

        /// \brief 是否所有属性都有效
        /// \return true-所有属性都有效，false-存在无效属性
        virtual bool IsAllValid() const = 0;

        /// \brief 设置颜色是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetColorValidity(bool overrideOrNot) = 0;

        /// \brief 颜色是否有效
        /// \return true-有效，false-无效
        virtual bool IsColorValid() const = 0;

        /// \brief 设置透明度是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetTransparencyValidity(bool overrideOrNot) = 0;

        /// \brief 透明度是否有效
        /// \return true-有效，false-没有效
        virtual bool IsTransparencyValid() const = 0;
        
        /// \brief 设置投影线颜色是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetProjectionLineColorValidity(bool overrideOrNot) = 0;

        /// \brief 投影线颜色是否有效
        /// \return true-有效，false-没有效
        virtual bool IsProjectionLineColorValid() const = 0;
        
        /// \brief 设置投影线线宽是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetProjectionLineWidthValidity(bool overrideOrNot) = 0;
        
        /// \brief 投影线线宽是否有效
        /// \return true-有效，false-没有效
        virtual bool IsProjectionLineWidthValid() const = 0;

        /// \brief 设置投影线打印线宽是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetProjectionLinePrintWidthValidity(bool overrideOrNot) = 0;

        /// \brief 投影线打印线宽是否有效
        /// \return true-有效，false-没有效
        virtual bool IsProjectionLinePrintWidthValid() const = 0;

        /// \brief 设置投影线线型是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetProjectionLineTypeNameValidity(bool overrideOrNot) = 0;

        /// \brief 投影线线型是否有效
        /// \return true-有效，false-没有效
        virtual bool IsProjectionLineTypeNameValid() const = 0;
        
        /// \brief 设置截面颜色是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetSectionFaceColorValidity(bool overrideOrNot) = 0;

        /// \brief 截面颜色是否有效
        /// \return true-有效，false-没有效
        virtual bool IsSectionFaceColorValid() const = 0;

        /// \brief 设置截面线颜色是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetSectionLineColorValidity(bool overrideOrNot) = 0;

        /// \brief 截面线颜色是否有效
        /// \return true-有效，false-没有效
        virtual bool IsSectionLineColorValid() const = 0;
        
        /// \brief 设置截面线线宽是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetSectionLineWidthValidity(bool overrideOrNot) = 0;

        /// \brief 截面线线宽是否有效
        /// \return true-有效，false-没有效
        virtual bool IsSectionLineWidthValid() const = 0;

        /// \brief 设置截面线打印线宽是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetSectionLinePrintWidthValidity(bool overrideOrNot) = 0;

        /// \brief 截面线打印线宽是否有效
        /// \return true-有效，false-没有效
        virtual bool IsSectionLinePrintWidthValid() const = 0;

        /// \brief 设置截面线线型是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetSectionLineTypeNameValidity(bool overrideOrNot) = 0;

        /// \brief 截面线线型是否有效
        /// \return true-有效，false-没有效
        virtual bool IsSectionLineTypeNameValid() const = 0;

        /// \brief 设置隐藏线颜色是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetHiddenLineColorValidity(bool overrideOrNot) = 0;

        /// \brief 隐藏线颜色是否有效
        /// \return true-有效，false-没有效
        virtual bool IsHiddenLineColorValid() const = 0;

        /// \brief 设置隐藏线线宽是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetHiddenLineWidthValidity(bool overrideOrNot) = 0;

        /// \brief 隐藏线线宽是否有效
        /// \return true-有效，false-没有效
        virtual bool IsHiddenLineWidthValid() const = 0;

        /// \brief 设置隐藏线打印线宽是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetHiddenLinePrintWidthValidity(bool overrideOrNot) = 0;
       
        /// \brief 隐藏线打印线宽是否有效
        /// \return true-有效，false-没有效
        virtual bool IsHiddenLinePrintWidthValid() const = 0;

        /// \brief 设置隐藏线线型是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetHiddenLineTypeNameValidity(bool overrideOrNot) = 0;

        /// \brief 隐藏线线型是否有效
        /// \return true-有效，false-没有效
        virtual bool IsHiddenLineTypeNameValid() const = 0;
        
        /// \brief 设置投影面填充模式是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetProjectionFaceHatchPatternValidity(bool overrideOrNot) = 0;

        /// \brief 投影面填充模式是否有效
        /// \return true-有效，false-没有效
        virtual bool IsProjectionFaceHatchPatternValid() const = 0;

        /// \brief 设置投影面填充颜色是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetProjectionFaceHatchColorValidity(bool overrideOrNot) = 0;

        /// \brief 投影面填充颜色是否有效
        /// \return true-有效，false-没有效
        virtual bool IsProjectionFaceHatchColorValid() const = 0;
        
        /// \brief 设置截面填充模式是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetSectionFaceHatchPatternValidity(bool overrideOrNot) = 0;

        /// \brief 截面填充模式是否有效
        /// \return true-有效，false-没有效
        virtual bool IsSectionFaceHatchPatternValid() const = 0;

        /// \brief 设置智能颜色模式是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetSmartColorModeValidity(bool overrideOrNot) = 0;

        /// \brief 智能颜色模式是否有效
        /// \return true-有效，false-没有效
        virtual bool IsSmartColorModeValid() const = 0;

        /// \brief 设置投影线智能颜色模式是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetProjectionLineSmartColorModeValidity(bool overrideOrNot) = 0;

        /// \brief 投影线智能颜色模式是否有效
        /// \return true-有效，false-没有效
        virtual bool IsProjectionLineSmartColorModeValid() const = 0;

        /// \brief 设置截面线智能颜色模式是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetSectionLineSmartColorModeValidity(bool overrideOrNot) = 0;

        /// \brief 截面线智能颜色模式是否有效
        /// \return true-有效，false-没有效
        virtual bool IsSectionLineSmartColorModeValid() const = 0;

        /// \brief 设置隐藏线智能颜色模式是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetHiddenLineSmartColorModeValidity(bool overrideOrNot) = 0;

        /// \brief 隐藏线智能颜色模式是否有效
        /// \return true-有效，false-没有效
        virtual bool IsHiddenLineSmartColorModeValid() const = 0;

        /// \brief 设置投影面填充智能颜色模式是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetProjectionFaceHatchSmartColorModeValidity(bool overrideOrNot) = 0;

        /// \brief 投影面填充智能颜色模式是否有效
        /// \return true-有效，false-没有效
        virtual bool IsProjectionFaceHatchSmartColorModeValid() const = 0;

        /// \brief 设置截面填充智能颜色模式是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetSectionFaceHatchSmartColorModeValidity(bool overrideOrNot) = 0;

        /// \brief 截面填充智能颜色模式是否有效
        /// \return true-有效，false-没有效
        virtual bool IsSectionFaceHatchSmartColorModeValid() const = 0;

        /// \brief 设置投影面填充缩放是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetProjectionFaceHatchScaleValidity(bool overrideOrNot) = 0;

        /// \brief 投影面填充缩放是否有效
        /// \return true-有效，false-没有效
        virtual bool IsProjectionFaceHatchScaleValid() const = 0;

        /// \brief 设置投影面填充旋转是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetProjectionFaceHatchRotationValidity(bool overrideOrNot) = 0;

        /// \brief 投影面填充旋转是否有效
        /// \return true-有效，false-没有效
        virtual bool IsProjectionFaceHatchRotationValid() const = 0;

        /// \brief 设置投影面填充线宽是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetProjectionFaceHatchLineWidthValidity(bool overrideOrNot) = 0;

        /// \brief 投影面填充线宽是否有效
        /// \return true-有效，false-没有效
        virtual bool IsProjectionFaceHatchLineWidthValid() const = 0;

        /// \brief 设置投影面填充打印线宽是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetProjectionFaceHatchLinePrintWidthValidity(bool overrideOrNot) = 0;
        
        /// \brief 投影面填充打印线宽是否有效
        /// \return true-有效，false-没有效
        virtual bool IsProjectionFaceHatchLinePrintWidthValid() const = 0;

        /// \brief 设置截面填充缩放是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetSectionFaceHatchScaleValidity(bool overrideOrNot) = 0;

        /// \brief 截面填充缩放是否有效
        /// \return true-有效，false-没有效
        virtual bool IsSectionFaceHatchScaleValid() const = 0;

        /// \brief 设置截面填充旋转是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetSectionFaceHatchRotationValidity(bool overrideOrNot) = 0;

        /// \brief 截面填充旋转是否有效
        /// \return true-有效，false-没有效
        virtual bool IsSectionFaceHatchRotationValid() const = 0;

        /// \brief 设置投影线线宽模式是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetProjectionLineWidthModeValidity(bool overrideOrNot) = 0;

        /// \brief 投影线线宽模式是否有效
        /// \return true-有效，false-没有效
        virtual bool IsProjectionLineWidthModeValid() const = 0;

        /// \brief 设置截面线线宽模式是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetSectionLineWidthModeValidity(bool overrideOrNot) = 0;

        /// \brief 截面线线宽模式是否有效
        /// \return true-有效，false-没有效
        virtual bool IsSectionLineWidthModeValid() const = 0;

        /// \brief 设置隐藏线线宽模式是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetHiddenLineWidthModeValidity(bool overrideOrNot) = 0;

        /// \brief 隐藏线线宽模式是否有效
        /// \return true-有效，false-没有效
        virtual bool IsHiddenLineWidthModeValid() const = 0;

        /// \brief 设置投影线线型缩放因子是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetProjectionLineTypeScaleValidity(bool overrideOrNot) = 0;

        /// \brief 投影线线型缩放因子是否有效
        /// \return true-有效，false-没有效
        virtual bool IsProjectionLineTypeScaleValid() const = 0;

        /// \brief 设置截面线线型缩放因子是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetSectionLineTypeScaleValidity(bool overrideOrNot) = 0;

        /// \brief 截面线线型缩放因子是否有效
        /// \return true-有效，false-没有效
        virtual bool IsSectionLineTypeScaleValid() const = 0;

        /// \brief 设置隐藏线线型缩放因子是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetHiddenLineTypeScaleValidity(bool overrideOrNot) = 0;

        /// \brief 截面线线型缩放因子是否有效
        /// \return true-有效，false-没有效
        virtual bool IsHiddenLineTypeScaleValid() const = 0;

        /// \brief 设置投影线本身的 alpha 值是否起效字段是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetEnableProjectionLineColorAlphaValidity(bool overrideOrNot) = 0;

        /// \brief 投影线本身的 alpha 值是否起效字段是否有效
        /// \return true-有效，false-没有效
        virtual bool IsEnableProjectionLineColorAlphaValid() const = 0;

        /// \brief 设置 投影面填充颜色的 alpha 值是否起效字段 是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetEnableProjectionFaceHatchColorAlphaValidity(bool overrideOrNot) = 0;

        /// \brief 投影面填充颜色的 alpha 值是否起效字段 是否有效
        /// \return true-有效，false-没有效
        virtual bool IsEnableProjectionFaceHatchColorAlphaValid() const = 0;

        /// \brief 设置 截面填充颜色的 alpha 值是否起效字段 是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetEnableSectionFaceHatchColorAlphaValidity(bool overrideOrNot) = 0;

        /// \brief 截面填充颜色的 alpha 值是否起效字段 是否有效
        /// \return true-有效，false-没有效
        virtual bool IsEnableSectionFaceHatchColorAlphaValid() const = 0;

        /// \brief 设置截面填充颜色是否有效
        /// \param overrideOrNot 是否有效
        /// \return 被修改的有效性设置接口自身
        virtual IGraphicsStyleDataValiditySettings& SetSectionFaceHatchColorValidity(bool overrideOrNot) = 0;

        /// \brief 截面填充颜色是否有效
        /// \return true-有效，false-没有效
        virtual bool IsSectionFaceHatchColorValid() const = 0;

        /// \brief 设置==符，判断两个图形样式数据各个属性的有效性设置是否相同
        /// \param another 需要对比的目标图形样式数据设置信息
        /// \return true-相同，false-不同
        virtual bool operator == (const IGraphicsStyleDataValiditySettings & another) const = 0;

        /// \brief 设置!=符，判断两个图形样式数据各个属性的有效性设置是否不同
        /// \param another 需要对比的目标图形样式数据有效性设置信息
        /// \return true-不同，false-相同
        virtual bool operator != (const IGraphicsStyleDataValiditySettings & another) const = 0;

        /// \brief 设置<符，判断两个图形样式数据各个属性的有效性设置的大小关系
        /// \param another 需要对比的目标图形样式数据有效性设置信息
        /// \return true-小于目标，false-大于等于目标
        virtual bool operator < (const IGraphicsStyleDataValiditySettings & another) const = 0;

        /// \brief 克隆
        ///
        /// 将自己复制一份
        /// \return 自己的副本
        virtual OwnerPtr<IGraphicsStyleDataValiditySettings> Clone() const = 0;
    };
}




