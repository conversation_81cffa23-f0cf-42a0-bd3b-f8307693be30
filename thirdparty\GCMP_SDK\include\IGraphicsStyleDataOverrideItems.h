// Owner: zhuxf
// Co-Owner:
// Reviewed

#pragma once

#include "GcmpModelInterface.h"

namespace gcmp
{

    /// \brief 图形样式的重载项
    ///
    /// 通过这个类，可以在视图中重载图形样式的个别字段而不是整个图形样式
    /// 如果接口有变动，需要同时更新接口 IModelView::GetGraphicsStyleOverrideByCategoryUid 的实现
    class GCMP_MODEL_INTERFACE_EXPORT IGraphicsStyleDataOverrideItems
    {
    public:
        /// \brief 析构函数
        virtual ~IGraphicsStyleDataOverrideItems() {}

#pragma region static methods
        /// \brief 创建接口实例
        /// \return 新创建的接口实例
        static OwnerPtr<IGraphicsStyleDataOverrideItems> Create();
#pragma endregion static methods

        /// \brief 取消所有重载
        /// \return 被修改的重载项接口自身
        virtual IGraphicsStyleDataOverrideItems& OverrideNone() = 0;

        /// \brief 是否所有字段都没有被重载
        /// \return true-所有字段都没有被重载，false-有字段被重载了
        virtual bool IsNoneOverridden() const = 0;

        /// \brief 重载所有字段
        /// \return 被修改的重载项接口自身
        virtual IGraphicsStyleDataOverrideItems& OverrideAll() = 0;

        /// \brief 是否所有字段被重载了
        /// \return true-所有字段都被重载了，false-有字段没有被重载
        virtual bool IsAllOverridden() const = 0;       

        /// \brief 重载点和投影面颜色
        /// \param overrideOrNot 是否重载
        /// \return 被修改的重载项接口自身
        virtual IGraphicsStyleDataOverrideItems& OverrideColor(bool overrideOrNot) = 0;

        /// \brief 点和投影面颜色是否被重载
        /// \return true-被重载，false-没被重载
        virtual bool IsColorOverridden() const = 0;

        /// \brief 重载透明度
        /// \param overrideOrNot 是否重载
        /// \return 被修改的重载项接口自身
        virtual IGraphicsStyleDataOverrideItems& OverrideTransparency(bool overrideOrNot) = 0;

        /// \brief 透明度是否被重载
        /// \return true-被重载，false-没被重载
        virtual bool IsTransparencyOverridden() const = 0;
        
        /// \brief 重载投影线颜色
        /// \param overrideOrNot 是否重载
        /// \return 被修改的重载项接口自身
        virtual IGraphicsStyleDataOverrideItems& OverrideProjectionLineColor(bool overrideOrNot) = 0;

        /// \brief 投影线颜色是否被重载
        /// \return true-被重载，false-没被重载
        virtual bool IsProjectionLineColorOverridden() const = 0;

        /// \brief 重载投影线线宽
        /// \param overrideOrNot 是否重载
        /// \return 被修改的重载项接口自身
        virtual IGraphicsStyleDataOverrideItems& OverrideProjectionLineWidth(bool overrideOrNot) = 0;

        /// \brief 投影线线宽是否被重载
        /// \return true-被重载，false-没被重载
        virtual bool IsProjectionLineWidthOverridden() const = 0;

        /// \brief 重载投影线打印线宽
        /// \param overrideOrNot 是否重载
        /// \return 被修改的重载项接口自身
        virtual IGraphicsStyleDataOverrideItems& OverrideProjectionLinePrintWidth(bool overrideOrNot) = 0;

        /// \brief 投影线打印线宽是否被重载
        /// \return true-被重载，false-没被重载
        virtual bool IsProjectionLinePrintWidthOverridden() const = 0;

        /// \brief 重载投影线线型比例
        ///  注意：gcmp画布上显示的线型，默认是pStyleData->GetProjectionLineTypeScale() / pModelView→GetViewScale()，
        ///            如设置显示比例无关线型，可以通过视图样式重载实现（视图比例改变时，始终抵消"/ pModelView→GetViewScale()"影响）
        /// \param overrideOrNot 是否重载
        /// \return 被修改的重载项接口自身
        virtual IGraphicsStyleDataOverrideItems& OverrideProjectionLineTypeScale(bool overrideOrNot) = 0;

        /// \brief 投影线线型比例是否被重载
        /// \return true-被重载，false-没被重载
        virtual bool IsProjectionLineTypeScaleOverridden() const = 0;


        /// \brief 重载投影线线型
        /// \param overrideOrNot 是否重载
        /// \return 被修改的重载项接口自身
        virtual IGraphicsStyleDataOverrideItems& OverrideProjectionLineTypeName(bool overrideOrNot) = 0;

        /// \brief 投影线线型是否被重载
        /// \return true-被重载，false-没被重载
        virtual bool IsProjectionLineTypeNameOverridden() const = 0;
        
        /// \brief 重载截面线颜色
        /// \param overrideOrNot 是否重载
        /// \return 被修改的重载项接口自身
        virtual IGraphicsStyleDataOverrideItems& OverrideSectionLineColor(bool overrideOrNot) = 0;

        /// \brief 截面线颜色是否被重载
        /// \return true-被重载，false-没被重载
        virtual bool IsSectionLineColorOverridden() const = 0;
        
        /// \brief 重载截面线线宽
        /// \param overrideOrNot 是否重载
        /// \return 被修改的重载项接口自身
        virtual IGraphicsStyleDataOverrideItems& OverrideSectionLineWidth(bool overrideOrNot) = 0;

        /// \brief 截面线线宽是否被重载
        /// \return true-被重载，false-没被重载
        virtual bool IsSectionLineWidthOverridden() const = 0;
        
        /// \brief 重载截面线打印线宽
        /// \param overrideOrNot 是否重载
        /// \return 被修改的重载项接口自身
        virtual IGraphicsStyleDataOverrideItems& OverrideSectionLinePrintWidth(bool overrideOrNot) = 0;

        /// \brief 截面线打印线宽是否被重载
        /// \return true-被重载，false-没被重载
        virtual bool IsSectionLinePrintWidthOverridden() const = 0;

        /// \brief 重载截面线线型
        /// \param overrideOrNot 是否重载
        /// \return 被修改的重载项接口自身
        virtual IGraphicsStyleDataOverrideItems& OverrideSectionLineTypeName(bool overrideOrNot) = 0;

        /// \brief 截面线线型是否被重载
        /// \return true-被重载，false-没被重载
        virtual bool IsSectionLineTypeNameOverridden() const = 0;

        /// \brief 重载隐藏线颜色
        /// \param overrideOrNot 是否重载
        /// \return 被修改的重载项接口自身
        virtual IGraphicsStyleDataOverrideItems& OverrideHiddenLineColor(bool overrideOrNot) = 0;

        /// \brief 隐藏线颜色是否被重载
        /// \return true-被重载，false-没被重载
        virtual bool IsHiddenLineColorOverridden() const = 0;

        /// \brief 重载隐藏线线宽
        /// \param overrideOrNot 是否重载
        /// \return 被修改的重载项接口自身
        virtual IGraphicsStyleDataOverrideItems& OverrideHiddenLineWidth(bool overrideOrNot) = 0;

        /// \brief 隐藏线线宽是否被重载
        /// \return true-被重载，false-没被重载
        virtual bool IsHiddenLineWidthOverridden() const = 0;

        /// \brief 重载隐藏线打印线宽
        /// \param overrideOrNot 是否重载
        /// \return 被修改的重载项接口自身
        virtual IGraphicsStyleDataOverrideItems& OverrideHiddenLinePrintWidth(bool overrideOrNot) = 0;

        /// \brief 隐藏线打印线宽是否被重载
        /// \return true-被重载，false-没被重载
        virtual bool IsHiddenLinePrintWidthOverridden() const = 0;

        /// \brief 重载隐藏线线型
        /// \param overrideOrNot 是否重载
        /// \return 被修改的重载项接口自身
        virtual IGraphicsStyleDataOverrideItems& OverrideHiddenLineTypeName(bool overrideOrNot) = 0;

        /// \brief 隐藏线线型是否被重载
        /// \return true-被重载，false-没被重载
        virtual bool IsHiddenLineTypeNameOverridden() const = 0;
        
        /// \brief 重载投影面填充模式
        /// \param overrideOrNot 是否重载
        /// \return 被修改的重载项接口自身
        virtual IGraphicsStyleDataOverrideItems& OverrideProjectionFaceHatchPattern(bool overrideOrNot) = 0;

        /// \brief 投影面填充模式是否被重载
        /// \return true-被重载，false-没被重载
        virtual bool IsProjectionFaceHatchPatternOverridden() const = 0;

        /// \brief 重载投影面填充颜色
        /// \param overrideOrNot 是否重载
        /// \return 被修改的重载项接口自身
        virtual IGraphicsStyleDataOverrideItems& OverrideProjectionFaceHatchColor(bool overrideOrNot) = 0;

        /// \brief 投影面填充颜色是否被重载
        /// \return true-被重载，false-没被重载
        virtual bool IsProjectionFaceHatchColorOverridden() const = 0;
        
        /// \brief 重载投影面填充打印线宽
        /// \param overrideOrNot 是否重载
        /// \return 被修改的重载项接口自身
        virtual IGraphicsStyleDataOverrideItems& OverrideProjectionFaceHatchLinePrintWidth(bool overrideOrNot) = 0;

        /// \brief 投影面填充打印线宽是否被重载
        /// \return true-被重载，false-没被重载
        virtual bool IsProjectionFaceHatchLinePrintWidthOverridden() const = 0;

        /// \brief 重载截面填充模式
        /// \param overrideOrNot 是否重载
        /// \return 被修改的重载项接口自身
        virtual IGraphicsStyleDataOverrideItems& OverrideSectionFaceHatchPattern(bool overrideOrNot) = 0;

        /// \brief 截面填充模式是否被重载
        /// \return true-被重载，false-没被重载
        virtual bool IsSectionFaceHatchPatternOverridden() const = 0;

        /// \brief 重载截面填充颜色
        /// \param overrideOrNot 是否重载
        /// \return 被修改的重载项接口自身
        virtual IGraphicsStyleDataOverrideItems& OverrideSectionFaceHatchColor(bool overrideOrNot) = 0;

        /// \brief 截面填充颜色是否被重载
        /// \return true-被重载，false-没被重载
        virtual bool IsSectionFaceHatchColorOverridden() const = 0;

        /// \brief 重载点和投影面智能颜色模式
        /// \param overrideOrNot 是否重载
        /// \return 被修改的重载项接口自身
        virtual IGraphicsStyleDataOverrideItems& OverrideSmartColorMode(bool overrideOrNot) = 0;

        /// \brief 点和投影面智能颜色模式是否被重载
        /// \return true-被重载，false-没被重载
        virtual bool IsSmartColorModeOverridden() const = 0;

        /// \brief 重载投影线智能颜色模式
        /// \param overrideOrNot 是否重载
        /// \return 被修改的重载项接口自身
        virtual IGraphicsStyleDataOverrideItems& OverrideProjectionLineSmartColorMode(bool overrideOrNot) = 0;

        /// \brief 投影线智能颜色模式是否被重载
        /// \return true-被重载，false-没被重载
        virtual bool IsProjectionLineSmartColorModeOverridden() const = 0;

        /// \brief 重载截面线智能颜色模式
        /// \param overrideOrNot 是否重载
        /// \return 被修改的重载项接口自身
        virtual IGraphicsStyleDataOverrideItems& OverrideSectionLineSmartColorMode(bool overrideOrNot) = 0;

        /// \brief 截面线智能颜色模式是否被重载
        /// \return true-被重载，false-没被重载
        virtual bool IsSectionLineSmartColorModeOverridden() const = 0;

        /// \brief 重载隐藏线智能颜色模式
        /// \param overrideOrNot 是否重载
        /// \return 被修改的重载项接口自身
        virtual IGraphicsStyleDataOverrideItems& OverrideHiddenLineSmartColorMode(bool overrideOrNot) = 0;

        /// \brief 隐藏线智能颜色模式是否被重载
        /// \return true-被重载，false-没被重载
        virtual bool IsHiddenLineSmartColorModeOverridden() const = 0;

        /// \brief 重载投影面填充智能颜色模式
        /// \param overrideOrNot 是否重载
        /// \return 被修改的重载项接口自身
        virtual IGraphicsStyleDataOverrideItems& OverrideProjectionFaceHatchSmartColorMode(bool overrideOrNot) = 0;

        /// \brief 投影面填充智能颜色模式是否被重载
        /// \return true-被重载，false-没被重载
        virtual bool IsProjectionFaceHatchSmartColorModeOverridden() const = 0;

        /// \brief 重载截面填充智能颜色模式
        /// \param overrideOrNot 是否重载
        /// \return 被修改的重载项接口自身
        virtual IGraphicsStyleDataOverrideItems& OverrideSectionFaceHatchSmartColorMode(bool overrideOrNot) = 0;

        /// \brief 截面填充智能颜色模式是否被重载
        /// \return true-被重载，false-没被重载
        virtual bool IsSectionFaceHatchSmartColorModeOverridden() const = 0;

        /// \brief 重载截面颜色
        /// \param overrideOrNot 是否重载
        /// \return 被修改的重载项接口自身
        virtual IGraphicsStyleDataOverrideItems& OverrideSectionFaceColor(bool overrideOrNot) = 0;

        /// \brief 截面颜色是否被重载
        /// \return true-被重载，false-没被重载
        virtual bool IsSectionFaceColorOverridden() const = 0;

        /// \brief 重载截面智能颜色模式
        /// \param overrideOrNot 是否重载
        /// \return 被修改的重载项接口自身
        virtual IGraphicsStyleDataOverrideItems& OverrideSectionFaceColorSmartColorMode(bool overrideOrNot) = 0;

        /// \brief 截面智能颜色模式是否被重载
        /// \return true-被重载，false-没被重载
        virtual bool IsSectionFaceColorSmartColorModeOverridden() const = 0;

        /// \brief 重载==符，判断两个图形样式数据重载信息是否相同
        /// \param another 需要对比的目标图形样式数据重载信息
        /// \return true-相同，false-不同
        virtual bool operator == (const IGraphicsStyleDataOverrideItems & another) const = 0;

        /// \brief 重载!=符，判断两个图形样式数据重载信息是否不同
        /// \param another 需要对比的目标图形样式数据重载信息
        /// \return true-不同，false-相同
        virtual bool operator != (const IGraphicsStyleDataOverrideItems & another) const = 0;

        /// \brief 重载<符，判断两个图形样式数据重载信息大小关系
        /// \param another 需要对比的目标图形样式数据重载信息
        /// \return true-小于目标，false-大于等于目标
        virtual bool operator < (const IGraphicsStyleDataOverrideItems & another) const = 0;

        /// \brief 克隆
        ///
        /// 将自己复制一份
        /// \return 自己的副本
        virtual OwnerPtr<IGraphicsStyleDataOverrideItems> Clone() const = 0;
    };
}




